// @ts-check
import { defineConfig } from 'astro/config';
import tailwindcss from "@tailwindcss/vite";

import icon from "astro-icon";

// https://astro.build/config
export default defineConfig({
  site: "https://hypercubed.ca",

  server: {
      port: 80,
      host: "*************",
      allowedHosts: ["hypercubed.ca", "www.hypercubed.ca"]
	},

  vite: {
      plugins: [tailwindcss()],
	},

  integrations: [icon()],
});