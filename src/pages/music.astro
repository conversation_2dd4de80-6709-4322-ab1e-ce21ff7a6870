---
// Music site; links to latest album as well as links to artist pages on all music platforms
import Layout from '../layouts/Layout.astro';
import Navbar from '../components/Navbar.astro';
import oceans_cover_art from '../assets/oceans_cover_art.png';
import adventures_cover_art from '../assets/adventures_cover_art.png';
import { Image } from 'astro:assets';
import { Icon } from 'astro-icon/components'
---

<Layout>
    <section>
        <Navbar />
    </section>
    <section class="p-4">
        <h2 class="text-2xl font-bold">Music</h2>
    </section>
    <section>
        <div class="grid grid-cols-3 gap-10">
            <div>
                <h3 class="text-xl font-bold text-center">Oceans</h3>
                <!-- add a blur because this isn't released yet -->
                <Image src={oceans_cover_art} alt="oceans" />
                <p>New EP releasing September 12th</p>
                <div>
                    <a href="https://go.protonradio.com/r/rl22w5uPgwQls">Presave</a>
                </div>
            </div>
            <div>
                <h3 class="text-xl font-bold text-center">Adventures</h3>
                <Image src={adventures_cover_art} alt="adventures" />
                <div class="flex flex-col justify-center">
                    <div class="flex justify-center">
                        <a href="https://open.spotify.com/album/014JO3eY89EOwUmqshm4XD?si=XHVXer1vRjqWUg3_qWfm5g">
                            <Icon name="mdi:spotify" />
                        </a>
                    </div>
                    <div class="flex justify-center">
                        <a href="https://music.apple.com/ca/album/adventures-single/1762839047">
                            <Icon name="mdi:apple" />
                        </a>
                    </div>
                    <div class="flex justify-center">
                        <a href="https://soundcloud.com/musiconchannel/sets/hypercubed-adventures">
                            <Icon name="mdi:soundcloud" />
                        </a>
                    </div>
                </div>
            </div>
    </section>
</Layout>
